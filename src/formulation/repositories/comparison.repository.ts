import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";
import { MaterialCriteriaDto } from "../dto/comparison-request.dto";
import { CriteriaDto } from "../dto/criteria.dto";
import { CriteriaQueryBuilder } from "./criteria-query.builder";
import { AccessStatus } from "@/formulation/enum/access-status.enum";
import { FormulationAccess } from "@/formulation/utils/formulation-access";

export interface FormulationAccessData {
  id: string
  ownerId: string
  requests: {
    id: string
    requesterId: string
    status: string
  }[]
}

export interface FormulationWithMaterials {
  id: string
  name: string
  grade: string
  materials: {
    materialId: string
    percentage: number
    material: {
      type: string
      reference: string
    }
  }[]
  testResults: {
    id: string
    testName: string
    propertyName: string
    standard: string | null
    condition: string | null
    value: number
    minRange: number | null
    maxRange: number | null
    searched: boolean
  }[]
}

export interface FormulationComparison {
  accessStatus: AccessStatus
  formulationId: string
  grade: string
  isAccessible: boolean
  matchingMaterialsCount: number
  materials: {
    materialId: string
    materialType: string
    reference: string
    searched: boolean
    value: number
  }[]
  name: string
  optionalCriteriaMatched: number
  testResults: {
    condition: string | null
    id: string
    maxRange: number | undefined
    minRange: number | undefined
    propertyName: string
    searched: boolean
    standard: string | null
    testName: string
    value: number
  }[]
  tier: number
  totalOptionalCriteria: number
}

@Injectable()
export class ComparisonRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async getFormulations(
    page = 1,
    limit = 10,
    userId?: string,
    criteria?: CriteriaDto[],
    materialCriteria?: MaterialCriteriaDto[],
    userRole?: string,
  ) {
    const skip = (page - 1) * limit;
    const requiredCriteria = criteria?.filter(c => c.required) ?? [];
    const optionalCriteria = criteria?.filter(c => !c.required) ?? [];

    const queryBuilder = new CriteriaQueryBuilder();
    const query = queryBuilder.buildCompleteQuery(requiredCriteria, optionalCriteria, materialCriteria);
    const queryParameters = queryBuilder.buildQueryParams(requiredCriteria, optionalCriteria, skip, limit, materialCriteria) as unknown[];

    const countBuilder = new CriteriaQueryBuilder();
    const countQuery = countBuilder.buildCountQuery(requiredCriteria, materialCriteria);
    const countParameters = countBuilder.buildCountParams(requiredCriteria, materialCriteria) as unknown[];

    const [rawResults, countResults] = await Promise.all([
      this.prisma.$queryRawUnsafe<unknown[]>(query, ...queryParameters),
      this.prisma.$queryRawUnsafe<{ count: bigint }[]>(countQuery, ...countParameters),
    ]);

    const totalResult = countResults as { count: bigint }[];
    const total = Number(totalResult[0]?.count ?? 0);

    const formulations = rawResults as Record<string, unknown>[];
    const formulationIds = formulations.map(f => String(f.id));

    const hasFullAccessRole = FormulationAccess.hasFullAccessRole(userRole);
    let formulationsAccessData: Map<string, FormulationAccessData> | undefined;
    if (!hasFullAccessRole) {
      formulationsAccessData = await this.getFormulationsAccessData(formulationIds);
    }

    const mappedFormulations = formulations.map(f =>
      this.mapRawResultToComparison(
        f,
        requiredCriteria,
        optionalCriteria,
        materialCriteria,
        userId,
        formulationsAccessData,
        hasFullAccessRole,
      ),
    );

    return {
      formulations: mappedFormulations,
      total,
    };
  }

  private async getFormulationsAccessData(formulationIds: string[]): Promise<Map<string, FormulationAccessData>> {
    if (formulationIds.length === 0) {
      return new Map();
    }

    const formulations = await this.prisma.formulation.findMany({
      select: {
        id: true,
        ownerId: true,
        requests: {
          select: {
            id: true,
            requesterId: true,
            status: true,
          },
        },
      },
      where: {
        id: {
          in: formulationIds,
        },
      },
    });

    return new Map(formulations.map(f => [f.id, f]));
  }

  private mapRawResultToComparison(
    rawResult: Record<string, unknown>,
    requiredCriteria: CriteriaDto[],
    optionalCriteria: CriteriaDto[],
    materialCriteria?: MaterialCriteriaDto[],
    userId?: string,
    formulationsAccessData?: Map<string, FormulationAccessData>,
    hasFullAccessRole = false,
  ): FormulationComparison {
    const formulationId = rawResult.id as string;
    const accessStatus = hasFullAccessRole ? AccessStatus.ACCESSED : this.determineAccessStatus(formulationId, userId, formulationsAccessData);
    const isAccessible = accessStatus === AccessStatus.ACCESSED;
    const allCriteria = [...requiredCriteria, ...optionalCriteria];
    const searchedMaterialIdSet = new Set(materialCriteria?.map(mc => mc.materialId) ?? []);

    return {
      accessStatus,
      formulationId: rawResult.id as string,
      grade: rawResult.grade as string,
      isAccessible,
      matchingMaterialsCount: isAccessible ? Number(rawResult.material_matches ?? 0) : 0,
      materials: isAccessible
        ? ((rawResult.materials ?? []) as Record<string, unknown>[]).map((m: Record<string, unknown>) => ({
            materialId: m.materialId as string,
            materialType: m.type as string,
            reference: m.reference as string,
            searched: searchedMaterialIdSet.has(m.materialId as string),
            value: m.percentage as number,
          }))
        : [],
      name: rawResult.name as string,
      optionalCriteriaMatched: Number(rawResult.optional_matches ?? 0),
      testResults: isAccessible
        ? ((rawResult.test_results ?? []) as Record<string, unknown>[]).map((tr: Record<string, unknown>) => ({
            condition: tr.condition as string | null,
            id: tr.id as string,
            maxRange: tr.maxRange ? Number(tr.maxRange) : undefined,
            minRange: tr.minRange ? Number(tr.minRange) : undefined,
            propertyName: tr.propertyName as string,
            searched: allCriteria.some(c => c.propertyName === tr.propertyName),
            standard: tr.standard as string | null,
            testName: tr.testName as string,
            value: Number(tr.value),
          }))
        : [],
      tier: Number(rawResult.tier ?? 1),
      totalOptionalCriteria: optionalCriteria.length,
    };
  }

  private determineAccessStatus(
    formulationId: string,
    userId?: string,
    formulationsAccessData?: Map<string, FormulationAccessData>,
  ): AccessStatus {
    if (!userId || !formulationId) {
      return AccessStatus.REQUIRED;
    }

    const formulation = formulationsAccessData?.get(formulationId);
    if (!formulation) {
      return AccessStatus.REQUIRED;
    }

    const isFormulationOwner = FormulationAccess.isFormulationOwner(formulation.ownerId, userId);
    if (isFormulationOwner) {
      return AccessStatus.ACCESSED;
    }

    const userAccessRequest = formulation.requests.find(
      request => request.requesterId === userId
    );

    if (!userAccessRequest) {
      return AccessStatus.REQUIRED;
    }

    return FormulationAccess.mapRequestStatusToAccessStatus(userAccessRequest.status);
  }
}
