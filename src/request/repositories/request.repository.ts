import { Injectable, UnprocessableEntityException } from "@nestjs/common";
import { SearchQueryBuilder } from "./search-query.builder";
import { Request, RequestStatus } from "@/generated/prisma";
import { PrismaService } from "@/prisma.service";

export interface RequestFilters {
  search?: string
  status?: RequestStatus
  page?: number
  limit?: number
}

@Injectable()
export class RequestRepository {
  constructor(
    private readonly prisma: PrismaService,
    private readonly searchQueryBuilder: SearchQueryBuilder
  ) {}

  async findAll(filters: RequestFilters): Promise<{ data: Request[], total: number, page: number, limit: number }> {
    const { search, status, page = 1, limit = 10 } = filters;
    const skip = (page - 1) * limit;

    const where: any = {};
    let requestIds: string[] = [];

    if (search) {
      const rawResults = await this.searchQueryBuilder.searchRequestWithRelations(search);
      requestIds = rawResults.map(r => r.id);

      if (requestIds.length === 0) {
        return { data: [], total: 0, page, limit };
      }

      where.id = { in: requestIds };
    }

    if (status) {
      where.status = status;
    }

    const [data, total] = await Promise.all([
      this.prisma.request.findMany({
        where,
        skip,
        take: limit,
        include: {
          requester: {
            include: {
              location: true,
              department: true,
            },
          },
          formulation: true,
        },
        orderBy: {
          id: "desc",
        },
      }),
      this.prisma.request.count({ where }),
    ]);

    return { data, total, page, limit };
  }

  async create(requesterId: string, formulationId: string): Promise<Request> {
    try {
      const request = await this.prisma.request.create({
        data: {
          requesterId,
          formulationId,
          status: RequestStatus.PENDING_APPROVAL,
        },
        include: {
          requester: {
            include: {
              location: true,
              department: true,
            },
          },
          formulation: true,
        },
      });

      return request;
    }
    catch (error) {
      if (error.code === "P2002") {
        throw new UnprocessableEntityException("Your request has been sent. We will send you a notification when your request is approved.");
      }
      throw error;
    }
  }

  async findById(id: string): Promise<(Request & { requester: any, formulation: any }) | null> {
    return this.prisma.request.findUnique({
      where: { id },
      include: {
        requester: {
          include: {
            location: true,
            department: true,
          },
        },
        formulation: true,
      },
    });
  }

  async updateStatus(id: string, status: RequestStatus): Promise<Request & { requester: any, formulation: any }> {
    return this.prisma.request.update({
      where: { id },
      data: { status },
      include: {
        requester: {
          include: {
            location: true,
            department: true,
          },
        },
        formulation: true,
      },
    });
  }

  async delete(id: string): Promise<void> {
    await this.prisma.request.delete({
      where: { id },
    });
  }
}
