import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { UserRole } from "../role/role.types";
import { UserService } from "../user/user.service";
import { CreateRequestDto, PaginatedRequestResponseDto, RequestQueryDto, RequestResponseDto } from "./dto";
import { RequestAction } from "./dto/request-action.enum";
import { NewFormulationRequestEvent } from "./events/new-formulation-request.event";
import { RequestStatusChangedEvent } from "./events/request-status-changed.event";
import { RequestRepository } from "./repositories";
import { createPaginatedResponse, normalizePaginationParameters } from "@/common/utils";
import { RequestStatus } from "@/generated/prisma";
import { PrismaService } from "@/prisma.service";
import { RoleService } from "@/role/role.service";

@Injectable()
export class RequestService {
  constructor(
    private readonly requestRepository: RequestRepository,
    private readonly prisma: PrismaService,
    private readonly userService: UserService,
    private readonly roleService: RoleService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  private mapToRequestResponseDto(request: any): RequestResponseDto {
    return {
      id: request.id,
      status: request.status,
      requester: request.requester,
      formulation: request.formulation,
    };
  }

  async findAll(query: RequestQueryDto): Promise<PaginatedRequestResponseDto> {
    const { search, status } = query;
    const { page, limit } = normalizePaginationParameters(query.page, query.limit);

    const result = await this.requestRepository.findAll({ search, status, page, limit });

    const mappedData = result.data.map((request: any) => this.mapToRequestResponseDto(request));

    const paginatedResponse = createPaginatedResponse({
      data: mappedData,
      total: result.total,
      page,
      limit,
    });

    return new PaginatedRequestResponseDto(paginatedResponse.data, paginatedResponse.meta);
  }

  async create(requesterId: string, createRequestDto: CreateRequestDto): Promise<RequestResponseDto> {
    const formulation = await this.prisma.formulation.findUnique({
      where: { id: createRequestDto.formulationId },
    });

    if (!formulation) {
      throw new NotFoundException("Formulation not found");
    }

    const request = await this.requestRepository.create(requesterId, createRequestDto.formulationId);

    const role = await this.roleService.findByCode(UserRole.ENGINEERING_MANAGER);
    const managers = await this.userService.findAll({ roleId: role.id, page: 1, limit: 1000 });

    const message = `A new request for formulation '${formulation.name}' has been submitted and is pending your approval.`;
    const html = `<html><body><h2>New Formulation Request</h2><p>Hello,</p><p>A new request for formulation <strong>${formulation.name}</strong> has been submitted.</p><p>Please review and take action in the system.</p><br><p>Thank you,<br/>Materiact Team</p></body></html>`;

    this.eventEmitter.emit(
      "request.new-formulation",
      new NewFormulationRequestEvent(
        formulation.name,
        managers.data.map(manager => manager.email),
        managers.data.map(manager => manager.id),
        requesterId,
        message,
        html
      )
    );

    return this.mapToRequestResponseDto(request);
  }

  async handleAction(userId: string, requestId: string, action: RequestAction): Promise<RequestResponseDto> {
    const request = await this.requestRepository.findById(requestId);
    if (!request) {
      throw new NotFoundException("Request not found");
    }
    if (request.status !== RequestStatus.PENDING_APPROVAL) {
      throw new BadRequestException("Only requests with status PENDING_APPROVAL can be approved or rejected");
    }
    let newStatus: RequestStatus;
    if (action === RequestAction.APPROVE) {
      newStatus = RequestStatus.APPROVED;
    }
    else if (action === RequestAction.REJECT) {
      newStatus = RequestStatus.REJECTED;
    }
    else {
      throw new BadRequestException("Invalid action");
    }
    const updatedRequest = await this.requestRepository.updateStatus(requestId, newStatus);

    this.eventEmitter.emit(
      "request.status-changed",
      new RequestStatusChangedEvent(
        request.requesterId,
        `${request.formulation.name} ${request.formulation.grade}`,
        newStatus
      )
    );

    return this.mapToRequestResponseDto(updatedRequest);
  }

  async revokeAccess(userId: string, requestId: string): Promise<void> {
    const request = await this.requestRepository.findById(requestId);
    if (!request) {
      throw new NotFoundException("Request not found");
    }

    await this.requestRepository.delete(requestId);
  }
}
